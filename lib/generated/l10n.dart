// GENERATED CODE - DO NOT MODIFY BY HAND
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'intl/messages_all.dart';

// **************************************************************************
// Generator: Flutter Intl IDE plugin
// Made by Localizely
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes

class S {
  S();

  static S? _current;

  static S get current {
    assert(
      _current != null,
      'No instance of S was loaded. Try to initialize the S delegate before accessing S.current.',
    );
    return _current!;
  }

  static const AppLocalizationDelegate delegate = AppLocalizationDelegate();

  static Future<S> load(Locale locale) {
    final name = (locale.countryCode?.isEmpty ?? false)
        ? locale.languageCode
        : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = S();
      S._current = instance;

      return instance;
    });
  }

  static S of(BuildContext context) {
    final instance = S.maybeOf(context);
    assert(
      instance != null,
      'No instance of S present in the widget tree. Did you add S.delegate in localizationsDelegates?',
    );
    return instance!;
  }

  static S? maybeOf(BuildContext context) {
    return Localizations.of<S>(context, S);
  }

  /// `تسجيل الدخول`
  String get login {
    return Intl.message('تسجيل الدخول', name: 'login', desc: '', args: []);
  }

  /// `التسجيل`
  String get register {
    return Intl.message('التسجيل', name: 'register', desc: '', args: []);
  }

  /// `البريد الإلكتروني`
  String get email {
    return Intl.message('البريد الإلكتروني', name: 'email', desc: '', args: []);
  }

  /// `ليس لديك حساب؟`
  String get dontHaveAnAccount {
    return Intl.message(
      'ليس لديك حساب؟',
      name: 'dontHaveAnAccount',
      desc: '',
      args: [],
    );
  }

  /// `لديك حساب؟`
  String get haveAnAccount {
    return Intl.message(
      'لديك حساب؟',
      name: 'haveAnAccount',
      desc: '',
      args: [],
    );
  }

  /// `كلمة المرور`
  String get password {
    return Intl.message('كلمة المرور', name: 'password', desc: '', args: []);
  }

  /// `تأكيد كلمة المرور`
  String get confirmPassword {
    return Intl.message(
      'تأكيد كلمة المرور',
      name: 'confirmPassword',
      desc: '',
      args: [],
    );
  }

  /// `نسيت كلمة المرور؟`
  String get forgotPassword {
    return Intl.message(
      'نسيت كلمة المرور؟',
      name: 'forgotPassword',
      desc: '',
      args: [],
    );
  }

  /// `إعادة تعيين كلمة المرور`
  String get resetPassword {
    return Intl.message(
      'إعادة تعيين كلمة المرور',
      name: 'resetPassword',
      desc: '',
      args: [],
    );
  }

  /// `رقم الهاتف المحمول`
  String get mobileNumber {
    return Intl.message(
      'رقم الهاتف المحمول',
      name: 'mobileNumber',
      desc: '',
      args: [],
    );
  }

  /// `سجل دخولك بحسابك الآن!`
  String get loginWithYourAccountNow {
    return Intl.message(
      'سجل دخولك بحسابك الآن!',
      name: 'loginWithYourAccountNow',
      desc: '',
      args: [],
    );
  }

  /// `سجل حسابك الآن!`
  String get registerWithYourAccountNow {
    return Intl.message(
      'سجل حسابك الآن!',
      name: 'registerWithYourAccountNow',
      desc: '',
      args: [],
    );
  }

  /// `تخطي`
  String get skip {
    return Intl.message('تخطي', name: 'skip', desc: '', args: []);
  }

  /// `المتاجر`
  String get stores {
    return Intl.message('المتاجر', name: 'stores', desc: '', args: []);
  }

  /// `الاسم الكامل`
  String get fullName {
    return Intl.message('الاسم الكامل', name: 'fullName', desc: '', args: []);
  }

  /// `التسجيل كمتجر؟`
  String get registerAsStore {
    return Intl.message(
      'التسجيل كمتجر؟',
      name: 'registerAsStore',
      desc: '',
      args: [],
    );
  }

  /// `التسجيل كطبيب؟`
  String get registerAsDoctor {
    return Intl.message(
      'التسجيل كطبيب؟',
      name: 'registerAsDoctor',
      desc: '',
      args: [],
    );
  }

  /// `الرئيسية`
  String get home {
    return Intl.message('الرئيسية', name: 'home', desc: '', args: []);
  }

  /// `لم تتلق رمز التحقق؟`
  String get didntReceiveCode {
    return Intl.message(
      'لم تتلق رمز التحقق؟',
      name: 'didntReceiveCode',
      desc: '',
      args: [],
    );
  }

  /// `الريلز`
  String get reels {
    return Intl.message('الريلز', name: 'reels', desc: '', args: []);
  }

  /// `المتاجر`
  String get shops {
    return Intl.message('المتاجر', name: 'shops', desc: '', args: []);
  }

  /// `الأطباء`
  String get doctors {
    return Intl.message('الأطباء', name: 'doctors', desc: '', args: []);
  }

  /// `عرض الكل`
  String get seeAll {
    return Intl.message('عرض الكل', name: 'seeAll', desc: '', args: []);
  }

  /// `مرحباً، {name}`
  String welcomeWithName(Object name) {
    return Intl.message(
      'مرحباً، $name',
      name: 'welcomeWithName',
      desc: '',
      args: [name],
    );
  }

  /// `الحيوانات`
  String get animals {
    return Intl.message('الحيوانات', name: 'animals', desc: '', args: []);
  }

  /// `المنتجات`
  String get products {
    return Intl.message('المنتجات', name: 'products', desc: '', args: []);
  }

  /// `عدد الشحنات`
  String get shipmentsCount {
    return Intl.message(
      'عدد الشحنات',
      name: 'shipmentsCount',
      desc: '',
      args: [],
    );
  }

  /// `حول`
  String get about {
    return Intl.message('حول', name: 'about', desc: '', args: []);
  }

  /// `لا يوجد اشخاص مهتمين بهذه الفئة`
  String get noPeopleInterestOnThis {
    return Intl.message(
      'لا يوجد اشخاص مهتمين بهذه الفئة',
      name: 'noPeopleInterestOnThis',
      desc: '',
      args: [],
    );
  }

  /// `اعثر على حيوانك الأليف المثالي`
  String get onBoardingTitle1 {
    return Intl.message(
      'اعثر على حيوانك الأليف المثالي',
      name: 'onBoardingTitle1',
      desc: '',
      args: [],
    );
  }

  /// `استكشف أفضل متاجر الحيوانات الأليفة`
  String get onBoardingTitle2 {
    return Intl.message(
      'استكشف أفضل متاجر الحيوانات الأليفة',
      name: 'onBoardingTitle2',
      desc: '',
      args: [],
    );
  }

  /// `اعتن بصحة حيوانك الأليف`
  String get onBoardingTitle3 {
    return Intl.message(
      'اعتن بصحة حيوانك الأليف',
      name: 'onBoardingTitle3',
      desc: '',
      args: [],
    );
  }

  /// `اكتشف مجموعة واسعة من الحيوانات الأليفة للتبني واصطحب الرفيق المثالي إلى المنزل.`
  String get onBoardingDescription1 {
    return Intl.message(
      'اكتشف مجموعة واسعة من الحيوانات الأليفة للتبني واصطحب الرفيق المثالي إلى المنزل.',
      name: 'onBoardingDescription1',
      desc: '',
      args: [],
    );
  }

  /// `تصفح متاجر الحيوانات الأليفة الموثوقة واعثر على أفضل المنتجات لأصدقائك ذوي الفراء.`
  String get onBoardingDescription2 {
    return Intl.message(
      'تصفح متاجر الحيوانات الأليفة الموثوقة واعثر على أفضل المنتجات لأصدقائك ذوي الفراء.',
      name: 'onBoardingDescription2',
      desc: '',
      args: [],
    );
  }

  /// `تواصل مع الأطباء البيطريين المحترفين وتأكد من حصول حيواناتك الأليفة على أفضل رعاية.`
  String get onBoardingDescription3 {
    return Intl.message(
      'تواصل مع الأطباء البيطريين المحترفين وتأكد من حصول حيواناتك الأليفة على أفضل رعاية.',
      name: 'onBoardingDescription3',
      desc: '',
      args: [],
    );
  }

  /// `التالي`
  String get next {
    return Intl.message('التالي', name: 'next', desc: '', args: []);
  }

  /// `ابدأ الآن`
  String get startNow {
    return Intl.message('ابدأ الآن', name: 'startNow', desc: '', args: []);
  }

  /// `الوصف`
  String get description {
    return Intl.message('الوصف', name: 'description', desc: '', args: []);
  }

  /// `العنوان`
  String get address {
    return Intl.message('العنوان', name: 'address', desc: '', args: []);
  }

  /// `أدخل`
  String get enter {
    return Intl.message('أدخل', name: 'enter', desc: '', args: []);
  }

  /// `اسم الطبيب`
  String get doctorName {
    return Intl.message('اسم الطبيب', name: 'doctorName', desc: '', args: []);
  }

  /// `البريد الإلكتروني (اختياري)`
  String get emailOptional {
    return Intl.message(
      'البريد الإلكتروني (اختياري)',
      name: 'emailOptional',
      desc: '',
      args: [],
    );
  }

  /// `شعار الطبيب`
  String get doctorLogo {
    return Intl.message('شعار الطبيب', name: 'doctorLogo', desc: '', args: []);
  }

  /// `حفظ`
  String get save {
    return Intl.message('حفظ', name: 'save', desc: '', args: []);
  }

  /// `إرسال`
  String get submit {
    return Intl.message('إرسال', name: 'submit', desc: '', args: []);
  }

  /// `اختيار صورة`
  String get pickImage {
    return Intl.message('اختيار صورة', name: 'pickImage', desc: '', args: []);
  }

  /// `تم اختيار الموقع بنجاح`
  String get locationPickedSuccessfully {
    return Intl.message(
      'تم اختيار الموقع بنجاح',
      name: 'locationPickedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `اختيار الموقع`
  String get pickLocation {
    return Intl.message(
      'اختيار الموقع',
      name: 'pickLocation',
      desc: '',
      args: [],
    );
  }

  /// `اضغط لاختيار الموقع`
  String get tapToSelectLocation {
    return Intl.message(
      'اضغط لاختيار الموقع',
      name: 'tapToSelectLocation',
      desc: '',
      args: [],
    );
  }

  /// `تغيير الموقع`
  String get changeLocation {
    return Intl.message(
      'تغيير الموقع',
      name: 'changeLocation',
      desc: '',
      args: [],
    );
  }

  /// `لم يتم العثور على بيانات`
  String get noDataFound {
    return Intl.message(
      'لم يتم العثور على بيانات',
      name: 'noDataFound',
      desc: '',
      args: [],
    );
  }

  /// `تغيير وسائل التواصل`
  String get changeSocial {
    return Intl.message(
      'تغيير وسائل التواصل',
      name: 'changeSocial',
      desc: '',
      args: [],
    );
  }

  /// `يرجى إضافة رابط صحيح`
  String get pleaseAddValidLink {
    return Intl.message(
      'يرجى إضافة رابط صحيح',
      name: 'pleaseAddValidLink',
      desc: '',
      args: [],
    );
  }

  /// `وسائل التواصل الاجتماعي`
  String get socialMedia {
    return Intl.message(
      'وسائل التواصل الاجتماعي',
      name: 'socialMedia',
      desc: '',
      args: [],
    );
  }

  /// `خلفية الطبيب`
  String get doctorBackground {
    return Intl.message(
      'خلفية الطبيب',
      name: 'doctorBackground',
      desc: '',
      args: [],
    );
  }

  /// `يرجى إضافة وسائل التواصل الاجتماعي الخاصة بك`
  String get pleaseAddYourSocialMedia {
    return Intl.message(
      'يرجى إضافة وسائل التواصل الاجتماعي الخاصة بك',
      name: 'pleaseAddYourSocialMedia',
      desc: '',
      args: [],
    );
  }

  /// `يرجى إضافة موقعك`
  String get pleaseAddYourLocation {
    return Intl.message(
      'يرجى إضافة موقعك',
      name: 'pleaseAddYourLocation',
      desc: '',
      args: [],
    );
  }

  /// `يمكنك أيضاً التسجيل كطبيب من ملفك الشخصي.`
  String get youCanAlsoRegisterAsDoctor {
    return Intl.message(
      'يمكنك أيضاً التسجيل كطبيب من ملفك الشخصي.',
      name: 'youCanAlsoRegisterAsDoctor',
      desc: '',
      args: [],
    );
  }

  /// `يمكنك أيضاً التسجيل كمتجر من ملفك الشخصي.`
  String get youCanAlsoRegisterAsStore {
    return Intl.message(
      'يمكنك أيضاً التسجيل كمتجر من ملفك الشخصي.',
      name: 'youCanAlsoRegisterAsStore',
      desc: '',
      args: [],
    );
  }

  /// `شعار المتجر`
  String get storeLogo {
    return Intl.message('شعار المتجر', name: 'storeLogo', desc: '', args: []);
  }

  /// `خلفية المتجر`
  String get storeBackground {
    return Intl.message(
      'خلفية المتجر',
      name: 'storeBackground',
      desc: '',
      args: [],
    );
  }

  /// `اسم المتجر`
  String get storeName {
    return Intl.message('اسم المتجر', name: 'storeName', desc: '', args: []);
  }

  /// `البحث`
  String get search {
    return Intl.message('البحث', name: 'search', desc: '', args: []);
  }

  /// `البحث عن المنتجات`
  String get searchForProducts {
    return Intl.message(
      'البحث عن المنتجات',
      name: 'searchForProducts',
      desc: '',
      args: [],
    );
  }

  /// `البحث عن المتاجر`
  String get searchForStores {
    return Intl.message(
      'البحث عن المتاجر',
      name: 'searchForStores',
      desc: '',
      args: [],
    );
  }

  /// `البحث عن الأطباء`
  String get searchForDoctors {
    return Intl.message(
      'البحث عن الأطباء',
      name: 'searchForDoctors',
      desc: '',
      args: [],
    );
  }

  /// `إنشاء حساب`
  String get createAccount {
    return Intl.message(
      'إنشاء حساب',
      name: 'createAccount',
      desc: '',
      args: [],
    );
  }

  /// `رقم الهوية`
  String get idNumber {
    return Intl.message('رقم الهوية', name: 'idNumber', desc: '', args: []);
  }

  /// `أقر بأنني قد قرأت ووافقت على الشروط والأحكام الخاصة باستخدام هذا التطبيق`
  String get termsAndConditions {
    return Intl.message(
      'أقر بأنني قد قرأت ووافقت على الشروط والأحكام الخاصة باستخدام هذا التطبيق',
      name: 'termsAndConditions',
      desc: '',
      args: [],
    );
  }

  /// `رقم الهوية غير صحيح`
  String get invalidIdNumber {
    return Intl.message(
      'رقم الهوية غير صحيح',
      name: 'invalidIdNumber',
      desc: '',
      args: [],
    );
  }

  /// `رقم الهاتف غير صحيح`
  String get invalidPhoneNumber {
    return Intl.message(
      'رقم الهاتف غير صحيح',
      name: 'invalidPhoneNumber',
      desc: '',
      args: [],
    );
  }

  /// `كلمات المرور غير متطابقة`
  String get passwordsDoNotMatch {
    return Intl.message(
      'كلمات المرور غير متطابقة',
      name: 'passwordsDoNotMatch',
      desc: '',
      args: [],
    );
  }

  /// `يرجى الموافقة على الشروط والأحكام`
  String get pleaseAcceptTerms {
    return Intl.message(
      'يرجى الموافقة على الشروط والأحكام',
      name: 'pleaseAcceptTerms',
      desc: '',
      args: [],
    );
  }

  /// `حدث خطأ`
  String get error {
    return Intl.message('حدث خطأ', name: 'error', desc: '', args: []);
  }

  /// `أقر بأنني قد قرأت وفهمت وأوافق على`
  String get termsAndConditionsText {
    return Intl.message(
      'أقر بأنني قد قرأت وفهمت وأوافق على',
      name: 'termsAndConditionsText',
      desc: '',
      args: [],
    );
  }

  /// `الشروط والأحكام`
  String get termsAndConditionsLink {
    return Intl.message(
      'الشروط والأحكام',
      name: 'termsAndConditionsLink',
      desc: '',
      args: [],
    );
  }

  /// `الخاصة باستخدام هذا التطبيق`
  String get termsAndConditionsEnd {
    return Intl.message(
      'الخاصة باستخدام هذا التطبيق',
      name: 'termsAndConditionsEnd',
      desc: '',
      args: [],
    );
  }

  /// `الشروط والأحكام`
  String get termsDialogTitle {
    return Intl.message(
      'الشروط والأحكام',
      name: 'termsDialogTitle',
      desc: '',
      args: [],
    );
  }

  /// `هذه هي الشروط والأحكام الخاصة بالتطبيق. يرجى قراءتها بعناية قبل الموافقة عليها. سيتم إضافة المحتوى الفعلي للشروط والأحكام هنا لاحقاً.`
  String get termsDialogContent {
    return Intl.message(
      'هذه هي الشروط والأحكام الخاصة بالتطبيق. يرجى قراءتها بعناية قبل الموافقة عليها. سيتم إضافة المحتوى الفعلي للشروط والأحكام هنا لاحقاً.',
      name: 'termsDialogContent',
      desc: '',
      args: [],
    );
  }

  /// `إغلاق`
  String get close {
    return Intl.message('إغلاق', name: 'close', desc: '', args: []);
  }

  /// `05xxxxxxxx`
  String get phoneHint {
    return Intl.message('05xxxxxxxx', name: 'phoneHint', desc: '', args: []);
  }

  /// `تسجيل الدخول`
  String get loginTitle {
    return Intl.message('تسجيل الدخول', name: 'loginTitle', desc: '', args: []);
  }

  /// `تذكرني`
  String get rememberMe {
    return Intl.message('تذكرني', name: 'rememberMe', desc: '', args: []);
  }

  /// `نسيت كلمة المرور؟`
  String get forgotPasswordLink {
    return Intl.message(
      'نسيت كلمة المرور؟',
      name: 'forgotPasswordLink',
      desc: '',
      args: [],
    );
  }

  /// `لا تملك حساب؟`
  String get dontHaveAccount {
    return Intl.message(
      'لا تملك حساب؟',
      name: 'dontHaveAccount',
      desc: '',
      args: [],
    );
  }

  /// `التحقق من الهاتف`
  String get verificationTitle {
    return Intl.message(
      'التحقق من الهاتف',
      name: 'verificationTitle',
      desc: '',
      args: [],
    );
  }

  /// `تم إرسال رمز التحقق إلى رقم هاتفك`
  String get verificationMessage {
    return Intl.message(
      'تم إرسال رمز التحقق إلى رقم هاتفك',
      name: 'verificationMessage',
      desc: '',
      args: [],
    );
  }

  /// `أدخل رمز التحقق`
  String get enterVerificationCode {
    return Intl.message(
      'أدخل رمز التحقق',
      name: 'enterVerificationCode',
      desc: '',
      args: [],
    );
  }

  /// `تحقق`
  String get verify {
    return Intl.message('تحقق', name: 'verify', desc: '', args: []);
  }

  /// `إعادة إرسال الرمز`
  String get resendCode {
    return Intl.message(
      'إعادة إرسال الرمز',
      name: 'resendCode',
      desc: '',
      args: [],
    );
  }

  /// `تم إرسال رمز التحقق بنجاح`
  String get verificationCodeSent {
    return Intl.message(
      'تم إرسال رمز التحقق بنجاح',
      name: 'verificationCodeSent',
      desc: '',
      args: [],
    );
  }

  /// `رمز التحقق غير صحيح`
  String get invalidVerificationCode {
    return Intl.message(
      'رمز التحقق غير صحيح',
      name: 'invalidVerificationCode',
      desc: '',
      args: [],
    );
  }

  /// `تم تسجيل الدخول بنجاح`
  String get loginSuccessful {
    return Intl.message(
      'تم تسجيل الدخول بنجاح',
      name: 'loginSuccessful',
      desc: '',
      args: [],
    );
  }

  /// `تم التسجيل بنجاح`
  String get registrationSuccessful {
    return Intl.message(
      'تم التسجيل بنجاح',
      name: 'registrationSuccessful',
      desc: '',
      args: [],
    );
  }

  /// `الشحنات النشطة`
  String get activeShipments {
    return Intl.message(
      'الشحنات النشطة',
      name: 'activeShipments',
      desc: '',
      args: [],
    );
  }

  /// `شحناتي المستلمة`
  String get myReceivedShipments {
    return Intl.message(
      'شحناتي المستلمة',
      name: 'myReceivedShipments',
      desc: '',
      args: [],
    );
  }

  /// `شحناتي المرسلة`
  String get mySentShipments {
    return Intl.message(
      'شحناتي المرسلة',
      name: 'mySentShipments',
      desc: '',
      args: [],
    );
  }

  /// `لا توجد شحنات مستلمة`
  String get noReceivedShipments {
    return Intl.message(
      'لا توجد شحنات مستلمة',
      name: 'noReceivedShipments',
      desc: '',
      args: [],
    );
  }

  /// `لا توجد شحنات مرسلة`
  String get noSentShipments {
    return Intl.message(
      'لا توجد شحنات مرسلة',
      name: 'noSentShipments',
      desc: '',
      args: [],
    );
  }

  /// `خطأ في تحميل البيانات`
  String get dataLoadError {
    return Intl.message(
      'خطأ في تحميل البيانات',
      name: 'dataLoadError',
      desc: '',
      args: [],
    );
  }

  /// `من: `
  String get from {
    return Intl.message('من: ', name: 'from', desc: '', args: []);
  }

  /// `إلى: `
  String get to {
    return Intl.message('إلى: ', name: 'to', desc: '', args: []);
  }

  /// `تكلفة التوصيل: `
  String get price {
    return Intl.message('تكلفة التوصيل: ', name: 'price', desc: '', args: []);
  }

  /// `شيكل`
  String get currency {
    return Intl.message('شيكل', name: 'currency', desc: '', args: []);
  }

  /// `الصفحة الرئيسية`
  String get homePage {
    return Intl.message(
      'الصفحة الرئيسية',
      name: 'homePage',
      desc: '',
      args: [],
    );
  }

  /// `سجل الشحنات`
  String get shipmentsHistory {
    return Intl.message(
      'سجل الشحنات',
      name: 'shipmentsHistory',
      desc: '',
      args: [],
    );
  }

  /// `المحفظة`
  String get wallet {
    return Intl.message('المحفظة', name: 'wallet', desc: '', args: []);
  }

  /// `المنيو`
  String get menu {
    return Intl.message('المنيو', name: 'menu', desc: '', args: []);
  }

  /// `قريباً...`
  String get comingSoon {
    return Intl.message('قريباً...', name: 'comingSoon', desc: '', args: []);
  }

  /// `هذه الصفحة قيد التطوير`
  String get pageUnderDevelopment {
    return Intl.message(
      'هذه الصفحة قيد التطوير',
      name: 'pageUnderDevelopment',
      desc: '',
      args: [],
    );
  }

  /// `انشاء شحنة`
  String get createOrder {
    return Intl.message('انشاء شحنة', name: 'createOrder', desc: '', args: []);
  }

  /// `البحث عن شحنة`
  String get searchOrder {
    return Intl.message(
      'البحث عن شحنة',
      name: 'searchOrder',
      desc: '',
      args: [],
    );
  }

  /// `انشاء شحنة`
  String get createOrderTitle {
    return Intl.message(
      'انشاء شحنة',
      name: 'createOrderTitle',
      desc: '',
      args: [],
    );
  }

  /// `محطة الانطلاق`
  String get departureStation {
    return Intl.message(
      'محطة الانطلاق',
      name: 'departureStation',
      desc: '',
      args: [],
    );
  }

  /// `محطة التوصيل`
  String get deliveryStation {
    return Intl.message(
      'محطة التوصيل',
      name: 'deliveryStation',
      desc: '',
      args: [],
    );
  }

  /// `اسم المستلم`
  String get receiverName {
    return Intl.message(
      'اسم المستلم',
      name: 'receiverName',
      desc: '',
      args: [],
    );
  }

  /// `رقم المستلم`
  String get receiverPhone {
    return Intl.message(
      'رقم المستلم',
      name: 'receiverPhone',
      desc: '',
      args: [],
    );
  }

  /// `نوع الطلب`
  String get orderType {
    return Intl.message('نوع الطلب', name: 'orderType', desc: '', args: []);
  }

  /// `ملاحظات`
  String get notes {
    return Intl.message('ملاحظات', name: 'notes', desc: '', args: []);
  }

  /// `ملحوظة`
  String get note {
    return Intl.message('ملحوظة', name: 'note', desc: '', args: []);
  }

  /// `يجب تسليم الشحنة في حال تم انشائها في نصف ساعه`
  String get deliveryNote {
    return Intl.message(
      'يجب تسليم الشحنة في حال تم انشائها في نصف ساعه',
      name: 'deliveryNote',
      desc: '',
      args: [],
    );
  }

  /// `انشاء الطلب`
  String get confirmOrder {
    return Intl.message(
      'انشاء الطلب',
      name: 'confirmOrder',
      desc: '',
      args: [],
    );
  }

  /// `ملخص الطلب`
  String get orderSummary {
    return Intl.message('ملخص الطلب', name: 'orderSummary', desc: '', args: []);
  }

  /// `انشاء الشحنة`
  String get confirmOrderCreation {
    return Intl.message(
      'انشاء الشحنة',
      name: 'confirmOrderCreation',
      desc: '',
      args: [],
    );
  }

  /// `تعديل بيانات`
  String get editData {
    return Intl.message('تعديل بيانات', name: 'editData', desc: '', args: []);
  }

  /// `إلغاء`
  String get cancel {
    return Intl.message('إلغاء', name: 'cancel', desc: '', args: []);
  }

  /// `تأكيد`
  String get confirm {
    return Intl.message('تأكيد', name: 'confirm', desc: '', args: []);
  }

  /// `لا يمكن اختيار نفس المحطة للانطلاق والتوصيل`
  String get cannotSelectSameStation {
    return Intl.message(
      'لا يمكن اختيار نفس المحطة للانطلاق والتوصيل',
      name: 'cannotSelectSameStation',
      desc: '',
      args: [],
    );
  }

  /// `يرجى اختيار محطة الانطلاق`
  String get pleaseSelectDepartureStation {
    return Intl.message(
      'يرجى اختيار محطة الانطلاق',
      name: 'pleaseSelectDepartureStation',
      desc: '',
      args: [],
    );
  }

  /// `يرجى اختيار محطة التوصيل`
  String get pleaseSelectDeliveryStation {
    return Intl.message(
      'يرجى اختيار محطة التوصيل',
      name: 'pleaseSelectDeliveryStation',
      desc: '',
      args: [],
    );
  }

  /// `يرجى إدخال اسم المستلم`
  String get pleaseEnterReceiverName {
    return Intl.message(
      'يرجى إدخال اسم المستلم',
      name: 'pleaseEnterReceiverName',
      desc: '',
      args: [],
    );
  }

  /// `يرجى إدخال رقم المستلم`
  String get pleaseEnterReceiverPhone {
    return Intl.message(
      'يرجى إدخال رقم المستلم',
      name: 'pleaseEnterReceiverPhone',
      desc: '',
      args: [],
    );
  }

  /// `يرجى اختيار نوع الطلب`
  String get pleaseSelectOrderType {
    return Intl.message(
      'يرجى اختيار نوع الطلب',
      name: 'pleaseSelectOrderType',
      desc: '',
      args: [],
    );
  }

  /// `تنسيق رقم الهاتف غير صحيح`
  String get invalidPhoneFormat {
    return Intl.message(
      'تنسيق رقم الهاتف غير صحيح',
      name: 'invalidPhoneFormat',
      desc: '',
      args: [],
    );
  }

  /// `تم إنشاء الطلب بنجاح`
  String get orderCreatedSuccessfully {
    return Intl.message(
      'تم إنشاء الطلب بنجاح',
      name: 'orderCreatedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `البحث عن شحنة`
  String get searchShipment {
    return Intl.message(
      'البحث عن شحنة',
      name: 'searchShipment',
      desc: '',
      args: [],
    );
  }

  /// `البحث عن شحنة`
  String get searchShipmentTitle {
    return Intl.message(
      'البحث عن شحنة',
      name: 'searchShipmentTitle',
      desc: '',
      args: [],
    );
  }

  /// `محطة الانطلاق`
  String get fromStation {
    return Intl.message(
      'محطة الانطلاق',
      name: 'fromStation',
      desc: '',
      args: [],
    );
  }

  /// `محطة التوصيل`
  String get toStation {
    return Intl.message('محطة التوصيل', name: 'toStation', desc: '', args: []);
  }

  /// `البحث عن شحنة`
  String get searchShipmentButton {
    return Intl.message(
      'البحث عن شحنة',
      name: 'searchShipmentButton',
      desc: '',
      args: [],
    );
  }

  /// `تفاصيل البحث عن شحنة`
  String get searchShipmentDetails {
    return Intl.message(
      'تفاصيل البحث عن شحنة',
      name: 'searchShipmentDetails',
      desc: '',
      args: [],
    );
  }

  /// `المسار المحدد`
  String get selectedRoute {
    return Intl.message(
      'المسار المحدد',
      name: 'selectedRoute',
      desc: '',
      args: [],
    );
  }

  /// `الشحنات التي تصل إلى`
  String get shipmentsUpTo {
    return Intl.message(
      'الشحنات التي تصل إلى',
      name: 'shipmentsUpTo',
      desc: '',
      args: [],
    );
  }

  /// `الأشخاص المهتمون`
  String get interestedPeople {
    return Intl.message(
      'الأشخاص المهتمون',
      name: 'interestedPeople',
      desc: '',
      args: [],
    );
  }

  /// `فرصة الحصول على الطرد`
  String get deliveryChance {
    return Intl.message(
      'فرصة الحصول على الطرد',
      name: 'deliveryChance',
      desc: '',
      args: [],
    );
  }

  /// `وضع علامة على هذه الفئة`
  String get iAmHere {
    return Intl.message(
      'وضع علامة على هذه الفئة',
      name: 'iAmHere',
      desc: '',
      args: [],
    );
  }

  /// `يرجى اختيار محطة الانطلاق`
  String get pleaseSelectFromStation {
    return Intl.message(
      'يرجى اختيار محطة الانطلاق',
      name: 'pleaseSelectFromStation',
      desc: '',
      args: [],
    );
  }

  /// `يرجى اختيار محطة التوصيل`
  String get pleaseSelectToStation {
    return Intl.message(
      'يرجى اختيار محطة التوصيل',
      name: 'pleaseSelectToStation',
      desc: '',
      args: [],
    );
  }

  /// `لا يمكن اختيار نفس المحطة للانطلاق والتوصيل`
  String get cannotSelectSameStationForSearch {
    return Intl.message(
      'لا يمكن اختيار نفس المحطة للانطلاق والتوصيل',
      name: 'cannotSelectSameStationForSearch',
      desc: '',
      args: [],
    );
  }

  /// `لم يتم العثور على شحنات`
  String get noShipmentsFound {
    return Intl.message(
      'لم يتم العثور على شحنات',
      name: 'noShipmentsFound',
      desc: '',
      args: [],
    );
  }

  /// `جاري تحميل الشحنات...`
  String get loadingShipments {
    return Intl.message(
      'جاري تحميل الشحنات...',
      name: 'loadingShipments',
      desc: '',
      args: [],
    );
  }

  /// `تم إضافة الحضور بنجاح`
  String get attendanceAdded {
    return Intl.message(
      'تم إضافة الحضور بنجاح',
      name: 'attendanceAdded',
      desc: '',
      args: [],
    );
  }

  /// `تم إزالة الحضور بنجاح`
  String get attendanceRemoved {
    return Intl.message(
      'تم إزالة الحضور بنجاح',
      name: 'attendanceRemoved',
      desc: '',
      args: [],
    );
  }

  /// `تم إضافة الاهتمام بنجاح`
  String get interestAdded {
    return Intl.message(
      'تم إضافة الاهتمام بنجاح',
      name: 'interestAdded',
      desc: '',
      args: [],
    );
  }

  /// `الوقت المقدر للوصول`
  String get estimatedTravelTime {
    return Intl.message(
      'الوقت المقدر للوصول',
      name: 'estimatedTravelTime',
      desc: '',
      args: [],
    );
  }

  /// `دقيقة`
  String get minutes {
    return Intl.message('دقيقة', name: 'minutes', desc: '', args: []);
  }

  /// `غير قادر على الحصول على الموقع الحالي`
  String get unableToGetCurrentLocation {
    return Intl.message(
      'غير قادر على الحصول على الموقع الحالي',
      name: 'unableToGetCurrentLocation',
      desc: '',
      args: [],
    );
  }

  /// `هل أنت متأكد من أنك تريد تأكيد التسليم لمحطة الانطلاق؟`
  String get areYouSureConfirmDeliveryToDepartureStation {
    return Intl.message(
      'هل أنت متأكد من أنك تريد تأكيد التسليم لمحطة الانطلاق؟',
      name: 'areYouSureConfirmDeliveryToDepartureStation',
      desc: '',
      args: [],
    );
  }

  /// `تسليم لمحطة الانطلاق`
  String get deliverToDepartureStation {
    return Intl.message(
      'تسليم لمحطة الانطلاق',
      name: 'deliverToDepartureStation',
      desc: '',
      args: [],
    );
  }

  /// `هل أنت متأكد من أنك تريد تأكيد الاستلام من محطة الانطلاق؟`
  String get areYouSureConfirmPickupFromDepartureStation {
    return Intl.message(
      'هل أنت متأكد من أنك تريد تأكيد الاستلام من محطة الانطلاق؟',
      name: 'areYouSureConfirmPickupFromDepartureStation',
      desc: '',
      args: [],
    );
  }

  /// `استلام من محطة الانطلاق`
  String get pickupFromDepartureStation {
    return Intl.message(
      'استلام من محطة الانطلاق',
      name: 'pickupFromDepartureStation',
      desc: '',
      args: [],
    );
  }

  /// `هل أنت متأكد من أنك تريد تأكيد التسليم للوجهة؟`
  String get areYouSureConfirmDeliveryToDestination {
    return Intl.message(
      'هل أنت متأكد من أنك تريد تأكيد التسليم للوجهة؟',
      name: 'areYouSureConfirmDeliveryToDestination',
      desc: '',
      args: [],
    );
  }

  /// `تسليم لنقطة الهدف`
  String get deliverToDestination {
    return Intl.message(
      'تسليم لنقطة الهدف',
      name: 'deliverToDestination',
      desc: '',
      args: [],
    );
  }

  /// `لا يمكن {action} لأنك بعيد من المحطة ({distance} متر)`
  String cannotPerformActionTooFarFromStation(String action, String distance) {
    return Intl.message(
      'لا يمكن $action لأنك بعيد من المحطة ($distance متر)',
      name: 'cannotPerformActionTooFarFromStation',
      desc: '',
      args: [action, distance],
    );
  }

  /// `تأكيد تغيير الحالة`
  String get confirmStatusChange {
    return Intl.message(
      'تأكيد تغيير الحالة',
      name: 'confirmStatusChange',
      desc: '',
      args: [],
    );
  }

  /// `تم تحديث الحالة بنجاح`
  String get statusUpdatedSuccessfully {
    return Intl.message(
      'تم تحديث الحالة بنجاح',
      name: 'statusUpdatedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `خطأ في تحديث الحالة`
  String get errorUpdatingStatus {
    return Intl.message(
      'خطأ في تحديث الحالة',
      name: 'errorUpdatingStatus',
      desc: '',
      args: [],
    );
  }

  /// `تفاصيل الطلب`
  String get orderDetails {
    return Intl.message(
      'تفاصيل الطلب',
      name: 'orderDetails',
      desc: '',
      args: [],
    );
  }

  /// `إخفاء المسافة`
  String get hideDistance {
    return Intl.message(
      'إخفاء المسافة',
      name: 'hideDistance',
      desc: '',
      args: [],
    );
  }

  /// `فحص المسافة`
  String get checkDistance {
    return Intl.message(
      'فحص المسافة',
      name: 'checkDistance',
      desc: '',
      args: [],
    );
  }

  /// `حساب المسافة...`
  String get calculatingDistance {
    return Intl.message(
      'حساب المسافة...',
      name: 'calculatingDistance',
      desc: '',
      args: [],
    );
  }

  /// `تأكيد الاهتمام`
  String get confirmInterest {
    return Intl.message(
      'تأكيد الاهتمام',
      name: 'confirmInterest',
      desc: '',
      args: [],
    );
  }

  /// `هل أنت متأكد من أنك تريد إظهار الاهتمام؟`
  String get areYouSureShowInterest {
    return Intl.message(
      'هل أنت متأكد من أنك تريد إظهار الاهتمام؟',
      name: 'areYouSureShowInterest',
      desc: '',
      args: [],
    );
  }

  /// `تم إضافة الاهتمام بنجاح`
  String get interestAddedSuccessfully {
    return Intl.message(
      'تم إضافة الاهتمام بنجاح',
      name: 'interestAddedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `تسليم لمحطة الانطلاق`
  String get deliverToDepartureStationButton {
    return Intl.message(
      'تسليم لمحطة الانطلاق',
      name: 'deliverToDepartureStationButton',
      desc: '',
      args: [],
    );
  }

  /// `استلام من محطة الانطلاق`
  String get pickupFromDepartureStationButton {
    return Intl.message(
      'استلام من محطة الانطلاق',
      name: 'pickupFromDepartureStationButton',
      desc: '',
      args: [],
    );
  }

  /// `تسليم لنقطة الهدف`
  String get deliverToDestinationButton {
    return Intl.message(
      'تسليم لنقطة الهدف',
      name: 'deliverToDestinationButton',
      desc: '',
      args: [],
    );
  }

  /// `النقاط`
  String get points {
    return Intl.message('النقاط', name: 'points', desc: '', args: []);
  }

  /// `تم تعيين الطلب بنجاح`
  String get orderAssignedSuccessfully {
    return Intl.message(
      'تم تعيين الطلب بنجاح',
      name: 'orderAssignedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `رقم الباركود`
  String get barcodeNumber {
    return Intl.message(
      'رقم الباركود',
      name: 'barcodeNumber',
      desc: '',
      args: [],
    );
  }

  /// `تم تعيين الطلب لك بنجاح. يمكنك الآن البدء في عملية التوصيل.`
  String get orderAssignmentSuccessMessage {
    return Intl.message(
      'تم تعيين الطلب لك بنجاح. يمكنك الآن البدء في عملية التوصيل.',
      name: 'orderAssignmentSuccessMessage',
      desc: '',
      args: [],
    );
  }

  /// `موافق`
  String get ok {
    return Intl.message('موافق', name: 'ok', desc: '', args: []);
  }

  /// `سجل الطلبات`
  String get ordersHistory {
    return Intl.message(
      'سجل الطلبات',
      name: 'ordersHistory',
      desc: '',
      args: [],
    );
  }

  /// `شحناتي المرسلة`
  String get sentShipments {
    return Intl.message(
      'شحناتي المرسلة',
      name: 'sentShipments',
      desc: '',
      args: [],
    );
  }

  /// `شحناتي المستلمة`
  String get receivedShipments {
    return Intl.message(
      'شحناتي المستلمة',
      name: 'receivedShipments',
      desc: '',
      args: [],
    );
  }

  /// `بانتظار تسليم محطة الارسال`
  String get pending {
    return Intl.message(
      'بانتظار تسليم محطة الارسال',
      name: 'pending',
      desc: '',
      args: [],
    );
  }

  /// `تم التسليم لمحطة الارسال`
  String get confirmed {
    return Intl.message(
      'تم التسليم لمحطة الارسال',
      name: 'confirmed',
      desc: '',
      args: [],
    );
  }

  /// `مع المرسل`
  String get pickedUp {
    return Intl.message('مع المرسل', name: 'pickedUp', desc: '', args: []);
  }

  /// `شحنات منتهيه`
  String get delivered {
    return Intl.message('شحنات منتهيه', name: 'delivered', desc: '', args: []);
  }

  /// `بانتظار الاستلام من محطة الارسال`
  String get waitingForOrder {
    return Intl.message(
      'بانتظار الاستلام من محطة الارسال',
      name: 'waitingForOrder',
      desc: '',
      args: [],
    );
  }

  /// `بانتظار التسليم لمحطة الهدف`
  String get pickedUpDelivery {
    return Intl.message(
      'بانتظار التسليم لمحطة الهدف',
      name: 'pickedUpDelivery',
      desc: '',
      args: [],
    );
  }

  /// `شحنات منتهيه`
  String get deliveredShipments {
    return Intl.message(
      'شحنات منتهيه',
      name: 'deliveredShipments',
      desc: '',
      args: [],
    );
  }

  /// `لا توجد طلبات`
  String get noOrdersFound {
    return Intl.message(
      'لا توجد طلبات',
      name: 'noOrdersFound',
      desc: '',
      args: [],
    );
  }

  /// `جاري تحميل الطلبات...`
  String get loadingOrders {
    return Intl.message(
      'جاري تحميل الطلبات...',
      name: 'loadingOrders',
      desc: '',
      args: [],
    );
  }

  /// `النقاط الحالية`
  String get currentPoints {
    return Intl.message(
      'النقاط الحالية',
      name: 'currentPoints',
      desc: '',
      args: [],
    );
  }

  /// `شراء نقاط`
  String get buyPoints {
    return Intl.message('شراء نقاط', name: 'buyPoints', desc: '', args: []);
  }

  /// `سجل المعاملات`
  String get transactionsHistory {
    return Intl.message(
      'سجل المعاملات',
      name: 'transactionsHistory',
      desc: '',
      args: [],
    );
  }

  /// `لا توجد معاملات حتى الآن`
  String get noTransactionsYet {
    return Intl.message(
      'لا توجد معاملات حتى الآن',
      name: 'noTransactionsYet',
      desc: '',
      args: [],
    );
  }

  /// `تم اكتسابها`
  String get earnedPoints {
    return Intl.message(
      'تم اكتسابها',
      name: 'earnedPoints',
      desc: '',
      args: [],
    );
  }

  /// `تم استخدامها`
  String get spentPoints {
    return Intl.message(
      'تم استخدامها',
      name: 'spentPoints',
      desc: '',
      args: [],
    );
  }

  /// `اليوم`
  String get today {
    return Intl.message('اليوم', name: 'today', desc: '', args: []);
  }

  /// `الساعة`
  String get time {
    return Intl.message('الساعة', name: 'time', desc: '', args: []);
  }

  /// `نسيت كلمة المرور`
  String get forgotPasswordTitle {
    return Intl.message(
      'نسيت كلمة المرور',
      name: 'forgotPasswordTitle',
      desc: '',
      args: [],
    );
  }

  /// `التحقق من الهاتف`
  String get verifyPhoneTitle {
    return Intl.message(
      'التحقق من الهاتف',
      name: 'verifyPhoneTitle',
      desc: '',
      args: [],
    );
  }

  /// `كلمة مرور جديدة`
  String get newPasswordTitle {
    return Intl.message(
      'كلمة مرور جديدة',
      name: 'newPasswordTitle',
      desc: '',
      args: [],
    );
  }

  /// `إرسال الرمز`
  String get sendCode {
    return Intl.message('إرسال الرمز', name: 'sendCode', desc: '', args: []);
  }

  /// `إعادة تعيين كلمة المرور`
  String get resetPasswordButton {
    return Intl.message(
      'إعادة تعيين كلمة المرور',
      name: 'resetPasswordButton',
      desc: '',
      args: [],
    );
  }

  /// `أدخل رقم هاتفك لإرسال رمز التحقق`
  String get forgotPasswordMessage {
    return Intl.message(
      'أدخل رقم هاتفك لإرسال رمز التحقق',
      name: 'forgotPasswordMessage',
      desc: '',
      args: [],
    );
  }

  /// `أدخل كلمة المرور الجديدة`
  String get newPasswordMessage {
    return Intl.message(
      'أدخل كلمة المرور الجديدة',
      name: 'newPasswordMessage',
      desc: '',
      args: [],
    );
  }

  /// `الملف الشخصي`
  String get profile {
    return Intl.message('الملف الشخصي', name: 'profile', desc: '', args: []);
  }

  /// `تعديل الملف الشخصي`
  String get editProfile {
    return Intl.message(
      'تعديل الملف الشخصي',
      name: 'editProfile',
      desc: '',
      args: [],
    );
  }

  /// `المعلومات الشخصية`
  String get personalInfo {
    return Intl.message(
      'المعلومات الشخصية',
      name: 'personalInfo',
      desc: '',
      args: [],
    );
  }

  /// `الإشعارات`
  String get notifications {
    return Intl.message('الإشعارات', name: 'notifications', desc: '', args: []);
  }

  /// `سياسة الخصوصية`
  String get privacyPolicy {
    return Intl.message(
      'سياسة الخصوصية',
      name: 'privacyPolicy',
      desc: '',
      args: [],
    );
  }

  /// `عن التطبيق`
  String get aboutUs {
    return Intl.message('عن التطبيق', name: 'aboutUs', desc: '', args: []);
  }

  /// `تسجيل الخروج`
  String get logout {
    return Intl.message('تسجيل الخروج', name: 'logout', desc: '', args: []);
  }

  /// `هل أنت متأكد من تسجيل الخروج؟`
  String get logoutConfirmation {
    return Intl.message(
      'هل أنت متأكد من تسجيل الخروج؟',
      name: 'logoutConfirmation',
      desc: '',
      args: [],
    );
  }

  /// `تم تحديث الملف الشخصي بنجاح`
  String get profileUpdatedSuccessfully {
    return Intl.message(
      'تم تحديث الملف الشخصي بنجاح',
      name: 'profileUpdatedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `يتطلب تحديث رقم الهاتف التحقق`
  String get phoneVerificationRequired {
    return Intl.message(
      'يتطلب تحديث رقم الهاتف التحقق',
      name: 'phoneVerificationRequired',
      desc: '',
      args: [],
    );
  }

  /// `رقم الهاتف الجديد`
  String get newPhone {
    return Intl.message(
      'رقم الهاتف الجديد',
      name: 'newPhone',
      desc: '',
      args: [],
    );
  }

  /// `كلمة المرور الحالية`
  String get currentPassword {
    return Intl.message(
      'كلمة المرور الحالية',
      name: 'currentPassword',
      desc: '',
      args: [],
    );
  }

  /// `كلمة المرور الجديدة`
  String get newPassword {
    return Intl.message(
      'كلمة المرور الجديدة',
      name: 'newPassword',
      desc: '',
      args: [],
    );
  }

  /// `حفظ التغييرات`
  String get saveChanges {
    return Intl.message(
      'حفظ التغييرات',
      name: 'saveChanges',
      desc: '',
      args: [],
    );
  }
}

class AppLocalizationDelegate extends LocalizationsDelegate<S> {
  const AppLocalizationDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[
      Locale.fromSubtags(languageCode: 'en'),
      Locale.fromSubtags(languageCode: 'ar'),
    ];
  }

  @override
  bool isSupported(Locale locale) => _isSupported(locale);
  @override
  Future<S> load(Locale locale) => S.load(locale);
  @override
  bool shouldReload(AppLocalizationDelegate old) => false;

  bool _isSupported(Locale locale) {
    for (var supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == locale.languageCode) {
        return true;
      }
    }
    return false;
  }
}
